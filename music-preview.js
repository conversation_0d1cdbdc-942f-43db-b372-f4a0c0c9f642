class MusicPreview {
    constructor() {
        this.audio = document.getElementById('preview-audio');
        this.playButton = document.getElementById('preview-play-button');
        this.progressBar = document.getElementById('preview-progress');
        this.progressFill = document.getElementById('preview-progress-fill');
        this.albumCover = document.getElementById('preview-album-cover');
        this.songInfo = document.getElementById('preview-song-info');
        
        this.isPlaying = false;
        this.maxDuration = 30; // 30 Sekunden Preview
        this.startTime = 0;
        
        this.init();
    }
    
    init() {
        if (!this.audio || !this.playButton) return;
        
        this.setupEventListeners();
        this.setupAudio();
    }
    
    setupEventListeners() {
        // Play/Pause Button
        this.playButton.addEventListener('click', () => this.togglePlayPause());
        
        // Audio Events
        this.audio.addEventListener('loadedmetadata', () => this.onAudioLoaded());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('ended', () => this.onAudioEnded());
        this.audio.addEventListener('error', () => this.onAudioError());
        
        // Klick auf Cover oder Song-Info -> zur Musikseite mit Like Water
        if (this.albumCover) {
            this.albumCover.addEventListener('click', () => this.goToMusicPage());
        }
        if (this.songInfo) {
            this.songInfo.addEventListener('click', () => this.goToMusicPage());
        }
        
        // Progress Bar Klick
        if (this.progressBar) {
            this.progressBar.addEventListener('click', (e) => this.seekTo(e));
        }
    }
    
    setupAudio() {
        if (!this.audio) return;
        
        // Audio laden
        this.audio.load();
    }
    
    onAudioLoaded() {
        // Startzeit setzen (z.B. bei 10 Sekunden beginnen für bessere Preview)
        this.startTime = Math.min(10, this.audio.duration - this.maxDuration);
        this.audio.currentTime = this.startTime;
    }
    
    togglePlayPause() {
        if (!this.audio) return;
        
        if (this.isPlaying) {
            this.pause();
        } else {
            this.play();
        }
    }
    
    play() {
        if (!this.audio) return;
        
        // Sicherstellen, dass wir bei der Startzeit beginnen
        if (this.audio.currentTime < this.startTime || 
            this.audio.currentTime >= this.startTime + this.maxDuration) {
            this.audio.currentTime = this.startTime;
        }
        
        this.audio.play().then(() => {
            this.isPlaying = true;
            this.updatePlayButton();
        }).catch(error => {
            console.error('Fehler beim Abspielen:', error);
            this.onAudioError();
        });
    }
    
    pause() {
        if (!this.audio) return;
        
        this.audio.pause();
        this.isPlaying = false;
        this.updatePlayButton();
    }
    
    updatePlayButton() {
        if (!this.playButton) return;
        
        const playIcon = this.playButton.querySelector('.play-icon');
        const pauseIcon = this.playButton.querySelector('.pause-icon');
        
        if (this.isPlaying) {
            if (playIcon) playIcon.style.display = 'none';
            if (pauseIcon) pauseIcon.style.display = 'block';
        } else {
            if (playIcon) playIcon.style.display = 'block';
            if (pauseIcon) pauseIcon.style.display = 'none';
        }
    }
    
    updateProgress() {
        if (!this.audio || !this.progressFill) return;
        
        const currentTime = this.audio.currentTime;
        const relativeTime = currentTime - this.startTime;
        
        // Nach 30 Sekunden stoppen
        if (relativeTime >= this.maxDuration) {
            this.onAudioEnded();
            return;
        }
        
        // Progress berechnen (0-100%)
        const progress = Math.max(0, Math.min(100, (relativeTime / this.maxDuration) * 100));
        this.progressFill.style.width = progress + '%';
    }
    
    seekTo(event) {
        if (!this.audio || !this.progressBar) return;
        
        const rect = this.progressBar.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        
        const newTime = this.startTime + (percentage * this.maxDuration);
        this.audio.currentTime = Math.max(this.startTime, 
            Math.min(this.startTime + this.maxDuration, newTime));
    }
    
    onAudioEnded() {
        this.isPlaying = false;
        this.updatePlayButton();
        
        // Zurück zum Anfang der Preview
        this.audio.currentTime = this.startTime;
        this.progressFill.style.width = '0%';
    }
    
    onAudioError() {
        console.error('Fehler beim Laden der Audio-Datei');
        this.isPlaying = false;
        this.updatePlayButton();
        
        // Button deaktivieren bei Fehler
        if (this.playButton) {
            this.playButton.disabled = true;
            this.playButton.style.opacity = '0.5';
        }
    }
    
    goToMusicPage() {
        // Zur Musikseite mit Like Water als Parameter
        window.location.href = 'musik.html?play=Like%20Water';
    }
}

// Preview initialisieren wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    new MusicPreview();
});
