<!DOCTYPE html>
<html>
<head>
    <title>Debug Cover Loading</title>
</head>
<body>
    <h1>Debug Cover Loading</h1>
    <div id="result"></div>
    
    <script src="musik-data.js"></script>
    <script>
        // Finde den "We're Built for This" Song
        const song = MUSIK_DATEN.find(s => s.titel.includes("Built"));
        
        if (song) {
            document.getElementById('result').innerHTML = `
                <h2>Song gefunden:</h2>
                <p><strong>Titel:</strong> "${song.titel}"</p>
                <p><strong>Titel Länge:</strong> ${song.titel.length}</p>
                <p><strong>Character Codes:</strong> ${Array.from(song.titel).map(c => c.charCodeAt(0)).join(', ')}</p>
                <p><strong>Dateiname:</strong> ${song.dateiname}</p>
                <hr>
                <h3>Generierte Cover-Pfade:</h3>
            `;
            
            const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
            possibleFormats.forEach(format => {
                const coverPath = `bilder/album-covers/${song.titel}.${format}`;
                document.getElementById('result').innerHTML += `
                    <p><strong>${format.toUpperCase()}:</strong> ${coverPath}</p>
                `;
                
                // Test ob Bild existiert
                const img = new Image();
                img.onload = () => {
                    document.getElementById('result').innerHTML += `<p style="color: green;">✅ ${format.toUpperCase()} loads successfully!</p>`;
                };
                img.onerror = () => {
                    document.getElementById('result').innerHTML += `<p style="color: red;">❌ ${format.toUpperCase()} failed to load!</p>`;
                };
                img.src = coverPath;
            });
        } else {
            document.getElementById('result').innerHTML = '<p style="color: red;">Song nicht gefunden!</p>';
        }
    </script>
</body>
</html>
